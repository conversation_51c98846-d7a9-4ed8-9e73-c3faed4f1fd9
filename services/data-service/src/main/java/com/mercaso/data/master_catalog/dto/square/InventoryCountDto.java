package com.mercaso.data.master_catalog.dto.square;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class InventoryCountDto {

    @JsonProperty("catalog_object_id")
    private String catalogObjectId;
    @JsonProperty("catalog_object_type")
    private String catalogObjectType;
    @JsonProperty("state")
    private String state;
    @JsonProperty("location_id")
    private String locationId;
    @JsonProperty("quantity")
    private String quantity;
    @JsonProperty("calculated_at")
    private String calculatedAt;
}

