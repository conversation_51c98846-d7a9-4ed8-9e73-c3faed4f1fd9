package com.mercaso.wms.application.mapper.receivingtask;

import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReceivingTaskItemDtoApplicationMapper extends BaseDtoApplicationMapper<ReceivingTaskItem, ReceivingTaskItemDto> {

    ReceivingTaskItemDtoApplicationMapper INSTANCE = Mappers.getMapper(ReceivingTaskItemDtoApplicationMapper.class);

}
