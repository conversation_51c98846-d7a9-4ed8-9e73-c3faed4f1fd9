package com.mercaso.wms.delivery.application.event;

import com.mercaso.wms.application.dto.event.BusinessEventPayloadDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryOrderUnloadedPayloadDto extends BusinessEventPayloadDto<DeliveryOrderDto> {

    private UUID deliveryOrderId;

    @Builder
    public DeliveryOrderUnloadedPayloadDto(DeliveryOrderDto data, UUID deliveryOrderId) {
        super(data);
        this.deliveryOrderId = deliveryOrderId;
    }

}
