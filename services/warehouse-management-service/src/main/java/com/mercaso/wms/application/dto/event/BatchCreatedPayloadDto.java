package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.BatchDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BatchCreatedPayloadDto extends BusinessEventPayloadDto<BatchDto> {

    private UUID batchId;

    @Builder
    public BatchCreatedPayloadDto(BatchDto data, UUID batchId) {
        super(data);
        this.batchId = batchId;
    }

}
