package com.mercaso.wms.batch.strategy.impl;

import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.strategy.PopulateBreakdownStrategy;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FullBreakdownPopulateStrategy implements PopulateBreakdownStrategy {

    @Override
    public List<BreakdownDto> populateBreakdownTemplate(List<ExcelBatchDto> excelBatchDtoList) {
        Map<String, List<ExcelBatchDto>> filteredOrderMap = excelBatchDtoList.stream()
            .filter(ExcelBatchDto::isBigOrder)
            .collect(Collectors.groupingBy(ExcelBatchDto::getOrderNumber));

        List<BreakdownDto> breakdownDtos = Lists.newArrayList();
        filteredOrderMap.forEach((orderNumber, batchDtos) -> breakdownDtos.add(BreakdownDto.of(batchDtos)));
        breakdownDtos.sort(Comparator.comparing(BreakdownDto::getBreakdown, Comparator.nullsLast(Comparator.naturalOrder())));
        return breakdownDtos;
    }

}
