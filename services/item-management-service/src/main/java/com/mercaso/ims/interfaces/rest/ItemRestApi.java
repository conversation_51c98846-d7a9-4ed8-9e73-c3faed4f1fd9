package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemBackupVendorCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemPrimaryVendorCommand;
import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.command.UpdateItemUpcCommand;
import com.mercaso.ims.application.command.ValidateBarcodeCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPromoPriceResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemStatusResultDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ValidateBarcodeResultDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping(value = "/v1/item", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ItemRestApi {

    private static final String ITEM_ID_NOT_MATCH = "Item id not match";

    private final ItemQueryApplicationService itemQueryApplicationService;
    private final ItemApplicationService itemApplicationService;

    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public ItemDto getItemDetail(@PathVariable("id") UUID id) {
        log.info("[ItemDetail] param itemId: {}.", id);
        return itemQueryApplicationService.findById(id);
    }

    @PostMapping("/{id}/binding-photo")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto bindingPhoto(@PathVariable("id") UUID id, @RequestParam("requestFile") MultipartFile file) {
        ItemDto itemDto = itemApplicationService.bindingPhoto(id, file);
        log.info("[bindingPhoto] response itemDto: {}.", itemDto);
        return itemDto;
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public void deleted(@PathVariable("id") UUID id) {
        itemApplicationService.deleteItemById(DeleteItemCommand.builder().id(id).build());
    }

    @PutMapping("/active/{id}")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto activeItem(@PathVariable("id") UUID id) {
        ItemDto itemDto = itemApplicationService.active(id);
        log.info("[activeItem] response itemDto: {}.", itemDto);
        return itemDto;
    }

    @PutMapping("/draft/{id}")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto draftItem(@PathVariable("id") UUID id) {
        ItemDto itemDto = itemApplicationService.draft(id);
        log.info("[draftItem] response itemDto: {}.", itemDto);
        return itemDto;
    }


    @PutMapping("/archive/{id}")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto archiveItem(@PathVariable("id") UUID id) {
        ItemDto itemDto = itemApplicationService.archive(id);
        log.info("[archiveItem] response itemDto: {}.", itemDto);
        return itemDto;
    }

    @PostMapping
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto createItem(@RequestBody CreateItemCommand command) {
        log.info("[createItem] param command: {}.", command);
        Assert.isNull(command.getId(), "Item id should be null");
        Assert.isTrue(command.getPackageSize() != null && command.getPackageSize() > (0),
            "Package size should be greater than 0");
        Assert.notNull(command.getCategoryId(), "Category should be not null");

        return itemApplicationService.create(command);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto updateItem(@PathVariable("id") UUID id, @RequestBody UpdateItemCommand command) {
        log.info("[updateItem] id:{} param command: {}.", id, command);
        Assert.isTrue(command.getPackageSize() == null || command.getPackageSize() > (0),
            "Package size should be greater than 0");
        Assert.isTrue(command.getId().equals(id), ITEM_ID_NOT_MATCH);
        Assert.notNull(command.getCategoryId(), "Category should be not null");

        return itemApplicationService.update(command);
    }

    @PutMapping("/{id}/primary-vendor")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto setPrimaryVendor(@PathVariable("id") UUID id, @RequestBody UpdateItemPrimaryVendorCommand command) {
        Assert.isTrue(command.getId().equals(id), ITEM_ID_NOT_MATCH);
        return itemApplicationService.updatePrimaryVendor(command);
    }


    @PutMapping("/{id}/backup-vendor")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto setBackupVendor(@PathVariable("id") UUID id, @RequestBody UpdateItemBackupVendorCommand command) {
        Assert.isTrue(command.getId().equals(id), ITEM_ID_NOT_MATCH);
        return itemApplicationService.updateBackupVendor(command);
    }


    @PutMapping("/batch-update-status")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public BatchUpdateItemStatusResultDto batchUpdateStatus(@RequestBody BatchUpdateItemStatusCommand command) {
        log.info("[batchUpdateStatus] param command: {}.", command);
        return itemApplicationService.batchUpdateItemStatus(command);
    }

    @PostMapping("/barcode/validate")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public ValidateBarcodeResultDto validateBarcode(@RequestBody ValidateBarcodeCommand command) {
        log.info("Validate Barcode for :{}", command.getBarcode());
        return itemApplicationService.validateBarcode(command);
    }

    @PutMapping("/batch-update-photo")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public BatchUpdateItemPhotoResultDto batchUpdatePhoto(@RequestBody List<BatchUpdateItemPhotoCommand> commands) {
        log.info("[batchUpdatePhoto] param command: {}.", commands);
        return itemApplicationService.batchUpdateItemPhoto(commands);
    }

    @PutMapping("/{id}/promo-price")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto updatePromoPrice(@PathVariable("id") UUID id, @RequestBody UpdateItemPromoPriceCommand command) {
        log.info("[updatePromoPrice] param command: {}.", command);
        Assert.isTrue(command.getItemId().equals(id), ITEM_ID_NOT_MATCH);

        return itemApplicationService.updatePromoPrice(command);
    }

    @PutMapping("/{id}/upc")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemDto updateItemUpc(@PathVariable("id") UUID id, @RequestBody UpdateItemUpcCommand command) {
        log.info("[updateItemUpc] param command: {}.", command);
        Assert.isTrue(command.getItemId().equals(id), ITEM_ID_NOT_MATCH);

        return itemApplicationService.updateItemUpc(command);
    }

    @PutMapping("/batch-update-promo-price")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public BatchUpdateItemPromoPriceResultDto batchUpdatePromoPrice(@RequestBody List<UpdateItemPromoPriceCommand> commands) {
        log.info("[batchUpdatePromoPrice] param commands: {}.", commands);
        return itemApplicationService.batchUpdatePromoPrice(commands);
    }

    @PostMapping("/generate-promotional-image")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public String generatePromotionalImage(@RequestParam("requestFile") MultipartFile file, @RequestParam("regPrice") BigDecimal regPrice) {
        String itemDto = itemApplicationService.generatePromotionalImage(regPrice, file);
        log.info("[bindingPhoto] response itemDto: {}.", itemDto);
        return itemDto;
    }

}
