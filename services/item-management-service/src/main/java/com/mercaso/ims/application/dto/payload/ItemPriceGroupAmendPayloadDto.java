package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemPriceGroupAmendPayloadDto extends  BusinessEventPayloadDto<AmendDto<ItemPriceGroupDto>> {

    private UUID itemPriceGroupId;

    private ItemPriceGroupDto previous;

    private ItemPriceGroupDto current;

    @Builder
    public ItemPriceGroupAmendPayloadDto(ItemPriceGroupDto previous,ItemPriceGroupDto current,UUID itemPriceGroupId) {
        super(new AmendDto<>(previous, current));
        this.itemPriceGroupId = itemPriceGroupId;
        this.previous = previous;
        this.current = current;
    }
}