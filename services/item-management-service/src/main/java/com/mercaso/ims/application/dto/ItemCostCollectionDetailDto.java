package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemCostCollectionDetailDto extends BaseDto {

    private UUID id;

    private ItemCostCollectionSources source;

    private UUID vendorId;

    private String vendorName;
    
    private String vendorType;

    private String collectionNumber;

    private String vendorCollectionNumber;

    private ItemCostCollectionTypes type;

    private String fileName;

    private Integer pendingCount;

    private Integer approvedCount;

    private Integer rejectedCount;

    private Integer invalidCount;

    private Instant createdAt;

    private String createdBy;

    private String createdUserName;

    private Instant updatedAt;

    private String updatedUserName;

}