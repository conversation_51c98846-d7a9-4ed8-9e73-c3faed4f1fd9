package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto extends
    BusinessEventPayloadDto<ItemAdjustmentRequestDetailDto> {

    private UUID itemAdjustmentRequestDetailId;

    @Builder
    public ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto(ItemAdjustmentRequestDetailDto data,
        UUID itemAdjustmentRequestDetailId) {
        super(data);
        this.itemAdjustmentRequestDetailId = itemAdjustmentRequestDetailId;
    }
}