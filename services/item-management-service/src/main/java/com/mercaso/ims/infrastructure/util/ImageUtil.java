package com.mercaso.ims.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.*;

@Slf4j
public class ImageUtil {

    // Constants for image enhancement configuration
    private static final int MAX_IMAGE_WIDTH = 2000;
    private static final int MAX_IMAGE_HEIGHT = 2000;
    private static final int MIN_IMAGE_WIDTH = 100;
    private static final int MIN_IMAGE_HEIGHT = 100;

    // Triangle and text configuration
    private static final double TRIANGLE_SIZE_RATIO = 0.25; // Triangle size as ratio of image width
    private static final int MIN_TRIANGLE_SIZE = 80;
    private static final int MAX_TRIANGLE_SIZE = 200;

    // Font configuration
    private static final String FONT_FAMILY = "SansSerif";
    private static final int BASE_PROMO_FONT_SIZE = 16;
    private static final int BASE_PRICE_FONT_SIZE = 14;

    // Colors
    private static final Color TRIANGLE_COLOR = Color.YELLOW;
    private static final Color PROMO_TEXT_COLOR = Color.MAGENTA;
    private static final Color PRICE_TEXT_COLOR = Color.BLACK;

    // Margins and positioning
    private static final int TEXT_MARGIN = 10;
    private static final double ROTATION_ANGLE = Math.toRadians(-45); // Negative for better readability

    private ImageUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * Enhances an image by adding promotional text and price information
     *
     * @param file The image file to enhance
     * @param promoText The promotional text to add
     * @param originalPriceText The original price text to add with strikethrough
     * @return Enhanced image as byte array
     * @throws IllegalArgumentException if input parameters are invalid
     * @throws IOException if image processing fails
     */
    public static byte[] enhanceImage(MultipartFile file, String promoText, String originalPriceText)
            throws IllegalArgumentException, IOException {

        // Input validation
        validateInputs(file, promoText, originalPriceText);

        byte[] fileBytes = null;
        String fileExtension = null;

        try {
            fileBytes = file.getBytes();
            fileExtension = FileUtil.getFileExtension(fileBytes);
        } catch (IOException e) {
            log.error("Failed to read file bytes or determine file extension", e);
            throw new IOException("Failed to process input file", e);
        }

        // Validate file is an image
        if (!FileUtil.isImageFile(fileBytes)) {
            throw new IllegalArgumentException("File is not a valid image format");
        }

        BufferedImage image = null;
        try (InputStream inputStream = new ByteArrayInputStream(fileBytes)) {
            image = ImageIO.read(inputStream);
            if (image == null) {
                throw new IOException("Failed to read image - unsupported format or corrupted file");
            }
        } catch (IOException e) {
            log.error("Failed to read image from input stream", e);
            throw new IOException("Failed to read image", e);
        }

        // Validate image dimensions
        validateImageDimensions(image);

        // Create enhanced image
        BufferedImage enhancedImage = createEnhancedImage(image, promoText, originalPriceText);

        // Convert to byte array
        return convertImageToBytes(enhancedImage, fileExtension);
    }

    private static void validateInputs(MultipartFile file, String promoText, String originalPriceText) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be null or empty");
        }

        if (promoText == null || promoText.trim().isEmpty()) {
            throw new IllegalArgumentException("Promo text cannot be null or empty");
        }

        if (originalPriceText == null || originalPriceText.trim().isEmpty()) {
            throw new IllegalArgumentException("Original price text cannot be null or empty");
        }

        // Validate file size (10MB limit)
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("File size cannot exceed 10MB");
        }
    }

    private static void validateImageDimensions(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        if (width < MIN_IMAGE_WIDTH || height < MIN_IMAGE_HEIGHT) {
            throw new IllegalArgumentException(
                String.format("Image dimensions too small. Minimum: %dx%d, Actual: %dx%d",
                    MIN_IMAGE_WIDTH, MIN_IMAGE_HEIGHT, width, height));
        }

        if (width > MAX_IMAGE_WIDTH || height > MAX_IMAGE_HEIGHT) {
            throw new IllegalArgumentException(
                String.format("Image dimensions too large. Maximum: %dx%d, Actual: %dx%d",
                    MAX_IMAGE_WIDTH, MAX_IMAGE_HEIGHT, width, height));
        }
    }

    private static BufferedImage createEnhancedImage(BufferedImage originalImage, String promoText, String originalPriceText) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();

        // Create a copy of the original image to avoid modifying it
        BufferedImage enhancedImage = new BufferedImage(width, height, originalImage.getType());
        Graphics2D g = enhancedImage.createGraphics();

        try {
            // Draw original image
            g.drawImage(originalImage, 0, 0, null);

            // Configure graphics for high quality rendering
            configureGraphicsQuality(g);

            // Calculate triangle size based on image dimensions
            int triangleSize = calculateTriangleSize(width);

            // Draw promotional triangle and text
            drawPromotionalTriangle(g, triangleSize);
            drawPromotionalText(g, promoText, triangleSize);

            // Draw price text with strikethrough
            drawPriceText(g, originalPriceText, width, height);

        } finally {
            g.dispose();
        }

        return enhancedImage;
    }
