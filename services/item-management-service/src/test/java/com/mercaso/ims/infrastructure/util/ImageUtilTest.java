package com.mercaso.ims.infrastructure.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

class ImageUtilTest {

    private byte[] validJpegBytes;
    private byte[] validPngBytes;
    private byte[] invalidImageBytes;
    private byte[] tooSmallImageBytes;

    @BeforeEach
    void setUp() throws IOException {
        // Create a valid JPEG image
        validJpegBytes = createTestImage(500, 400, "jpg");
        
        // Create a valid PNG image
        validPngBytes = createTestImage(300, 300, "png");
        
        // Create invalid image bytes
        invalidImageBytes = "This is not an image".getBytes();
        
        // Create too small image
        tooSmallImageBytes = createTestImage(50, 50, "png");
    }

    @Test
    void testEnhanceImage_ValidJpegImage_ShouldSucceed() throws IOException {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", validJpegBytes);
        String promoText = "Special Offer!";
        String priceText = "$19.99";

        // Act
        byte[] result = ImageUtil.enhanceImage(file, promoText, priceText);

        // Assert
        assertNotNull(result);
        assertTrue(result.length > 0);
        assertTrue(FileUtil.isImageFile(result));
    }

    @Test
    void testEnhanceImage_ValidPngImage_ShouldSucceed() throws IOException {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.png", "test.png", "image/png", validPngBytes);
        String promoText = "Sale!";
        String priceText = "$9.99";

        // Act
        byte[] result = ImageUtil.enhanceImage(file, promoText, priceText);

        // Assert
        assertNotNull(result);
        assertTrue(result.length > 0);
        assertTrue(FileUtil.isImageFile(result));
    }

    @Test
    void testEnhanceImage_NullFile_ShouldThrowException() {
        // Arrange
        MultipartFile file = null;
        String promoText = "Special Offer!";
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("File cannot be null or empty", exception.getMessage());
    }

    @Test
    void testEnhanceImage_EmptyFile_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("empty.jpg", "empty.jpg", "image/jpeg", new byte[0]);
        String promoText = "Special Offer!";
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("File cannot be null or empty", exception.getMessage());
    }

    @Test
    void testEnhanceImage_NullPromoText_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", validJpegBytes);
        String promoText = null;
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("Promo text cannot be null or empty", exception.getMessage());
    }

    @Test
    void testEnhanceImage_EmptyPromoText_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", validJpegBytes);
        String promoText = "   ";
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("Promo text cannot be null or empty", exception.getMessage());
    }

    @Test
    void testEnhanceImage_NullPriceText_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", validJpegBytes);
        String promoText = "Special Offer!";
        String priceText = null;

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("Original price text cannot be null or empty", exception.getMessage());
    }

    @Test
    void testEnhanceImage_EmptyPriceText_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", validJpegBytes);
        String promoText = "Special Offer!";
        String priceText = "";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("Original price text cannot be null or empty", exception.getMessage());
    }

    @Test
    void testEnhanceImage_InvalidImageFile_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.txt", "test.txt", "text/plain", invalidImageBytes);
        String promoText = "Special Offer!";
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("File is not a valid image format", exception.getMessage());
    }

    @Test
    void testEnhanceImage_TooSmallImage_ShouldThrowException() {
        // Arrange
        MultipartFile file = new MockMultipartFile("small.png", "small.png", "image/png", tooSmallImageBytes);
        String promoText = "Special Offer!";
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertTrue(exception.getMessage().contains("Image dimensions too small"));
    }

    @Test
    void testEnhanceImage_LargeFile_ShouldThrowException() {
        // Arrange
        byte[] largeFileBytes = new byte[11 * 1024 * 1024]; // 11MB
        MultipartFile file = new MockMultipartFile("large.jpg", "large.jpg", "image/jpeg", largeFileBytes);
        String promoText = "Special Offer!";
        String priceText = "$19.99";

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> ImageUtil.enhanceImage(file, promoText, priceText));
        assertEquals("File size cannot exceed 10MB", exception.getMessage());
    }

    @Test
    void testEnhanceImage_LongTexts_ShouldHandleGracefully() throws IOException {
        // Arrange
        MultipartFile file = new MockMultipartFile("test.jpg", "test.jpg", "image/jpeg", validJpegBytes);
        String promoText = "This is a very long promotional text that should be handled properly";
        String priceText = "$999,999.99 - Very expensive item with long price text";

        // Act
        byte[] result = ImageUtil.enhanceImage(file, promoText, priceText);

        // Assert
        assertNotNull(result);
        assertTrue(result.length > 0);
        assertTrue(FileUtil.isImageFile(result));
    }

    @Test
    void testEnhanceImage_DifferentImageSizes_ShouldAdaptTriangleSize() throws IOException {
        // Arrange
        byte[] smallImageBytes = createTestImage(200, 200, "png");
        byte[] largeImageBytes = createTestImage(1000, 800, "png");
        
        MultipartFile smallFile = new MockMultipartFile("small.png", "small.png", "image/png", smallImageBytes);
        MultipartFile largeFile = new MockMultipartFile("large.png", "large.png", "image/png", largeImageBytes);
        
        String promoText = "Sale!";
        String priceText = "$19.99";

        // Act
        byte[] smallResult = ImageUtil.enhanceImage(smallFile, promoText, priceText);
        byte[] largeResult = ImageUtil.enhanceImage(largeFile, promoText, priceText);

        // Assert
        assertNotNull(smallResult);
        assertNotNull(largeResult);
        assertTrue(smallResult.length > 0);
        assertTrue(largeResult.length > 0);
        assertTrue(FileUtil.isImageFile(smallResult));
        assertTrue(FileUtil.isImageFile(largeResult));
    }

    /**
     * Helper method to create test images
     */
    private byte[] createTestImage(int width, int height, String format) throws IOException {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // Fill with white background
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);
        
        // Add some colored rectangles to make it a valid image
        g.setColor(Color.BLUE);
        g.fillRect(10, 10, width - 20, height - 20);
        g.setColor(Color.RED);
        g.fillRect(20, 20, width - 40, height - 40);
        
        g.dispose();
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, format, baos);
        return baos.toByteArray();
    }
}
